# ~/.bashrc: executed by bash(1) for non-login shells.

# 如果不是交互式shell，则直接返回
case $- in
    *i*) ;;
      *) return;;
esac

# 历史记录设置
HISTCONTROL=ignoreboth
shopt -s histappend
HISTSIZE=1000
HISTFILESIZE=2000

# 检查窗口大小并更新LINES和COLUMNS
shopt -s checkwinsize

# 设置颜色变量
if [ -x /usr/bin/tput ] && tput setaf 1 >&/dev/null; then
    # 支持颜色的终端
    color_prompt=yes
else
    color_prompt=
fi

# 定义颜色代码
RED='\[\033[0;31m\]'
GREEN='\[\033[0;32m\]'
YELLOW='\[\033[0;33m\]'
BLUE='\[\033[0;34m\]'
PURPLE='\[\033[0;35m\]'
CYAN='\[\033[0;36m\]'
WHITE='\[\033[0;37m\]'
BOLD='\[\033[1m\]'
RESET='\[\033[0m\]'

# 亮色版本
LIGHT_RED='\[\033[1;31m\]'
LIGHT_GREEN='\[\033[1;32m\]'
LIGHT_YELLOW='\[\033[1;33m\]'
LIGHT_BLUE='\[\033[1;34m\]'
LIGHT_PURPLE='\[\033[1;35m\]'
LIGHT_CYAN='\[\033[1;36m\]'

if [ "$color_prompt" = yes ]; then
    # 带颜色的PS1 - 用户名@主机名:路径$
    # 用户名为绿色，主机名为蓝色，路径为黄色
    PS1="${LIGHT_GREEN}\u${RESET}@${LIGHT_BLUE}\h${RESET}:${LIGHT_YELLOW}\w${RESET}\$ "
    
    # 另一个选项：更加丰富的颜色方案
    # PS1="${BOLD}${LIGHT_GREEN}\u${RESET}${WHITE}@${RESET}${BOLD}${LIGHT_BLUE}\h${RESET}${WHITE}:${RESET}${BOLD}${LIGHT_YELLOW}\w${RESET}${WHITE}\$ ${RESET}"
    
    # 第三个选项：包含时间的版本
    # PS1="${LIGHT_CYAN}[\t]${RESET} ${LIGHT_GREEN}\u${RESET}@${LIGHT_BLUE}\h${RESET}:${LIGHT_YELLOW}\w${RESET}\$ "
    
    # 第四个选项：简洁版本，只有路径带颜色
    # PS1="\u@\h:${LIGHT_YELLOW}\w${RESET}\$ "
else
    # 无颜色版本
    PS1='\u@\h:\w\$ '
fi

# 设置终端标题
case "$TERM" in
xterm*|rxvt*)
    PS1="\[\e]0;\u@\h: \w\a\]$PS1"
    ;;
*)
    ;;
esac

# 启用颜色支持
if [ -x /usr/bin/dircolors ]; then
    test -r ~/.dircolors && eval "$(dircolors -b ~/.dircolors)" || eval "$(dircolors -b)"
    alias ls='ls --color=auto'
    alias grep='grep --color=auto'
    alias fgrep='fgrep --color=auto'
    alias egrep='egrep --color=auto'
fi

# 一些有用的别名
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'

# 如果存在~/.bash_aliases，则加载它
if [ -f ~/.bash_aliases ]; then
    . ~/.bash_aliases
fi

# 启用可编程补全功能
if ! shopt -oq posix; then
  if [ -f /usr/share/bash-completion/bash_completion ]; then
    . /usr/share/bash-completion/bash_completion
  elif [ -f /etc/bash_completion ]; then
    . /etc/bash_completion
  fi
fi
